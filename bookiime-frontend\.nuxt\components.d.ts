
import type { DefineComponent, SlotsType } from 'vue'
type IslandComponent<T extends DefineComponent> = T & DefineComponent<{}, {refresh: () => Promise<void>}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, SlotsType<{ fallback: { error: unknown } }>>
type HydrationStrategies = {
  hydrateOnVisible?: IntersectionObserverInit | true
  hydrateOnIdle?: number | true
  hydrateOnInteraction?: keyof HTMLElementEventMap | Array<keyof HTMLElementEventMap> | true
  hydrateOnMediaQuery?: string
  hydrateAfter?: number
  hydrateWhen?: boolean
  hydrateNever?: true
}
type LazyComponent<T> = (T & DefineComponent<HydrationStrategies, {}, {}, {}, {}, {}, {}, { hydrated: () => void }>)
interface _GlobalComponents {
      'AppToast': typeof import("../components/app/toast.vue")['default']
    'AuthAnimatedBg': typeof import("../components/auth/animated-bg.vue")['default']
    'AuthFloatingShapes': typeof import("../components/auth/floating-shapes.vue")['default']
    'AuthLoginForm': typeof import("../components/auth/login/form.vue")['default']
    'AuthLoginShowcase': typeof import("../components/auth/login/showcase.vue")['default']
    'AuthSignupForm': typeof import("../components/auth/signup/form.vue")['default']
    'AuthSignupPasswordField': typeof import("../components/auth/signup/password-field.vue")['default']
    'AuthSignupPasswordStrengthIndicator': typeof import("../components/auth/signup/password-strength-indicator.vue")['default']
    'AuthSignupPhoneField': typeof import("../components/auth/signup/phone-field.vue")['default']
    'AuthSignupShowcase': typeof import("../components/auth/signup/showcase.vue")['default']
    'AuthSignupSubdomainField': typeof import("../components/auth/signup/subdomain-field.vue")['default']
    'InputField': typeof import("../components/input-field.vue")['default']
    'InputSelect': typeof import("../components/input-select.vue")['default']
    'InputTextarea': typeof import("../components/input-textarea.vue")['default']
    'MainHero': typeof import("../components/main/hero.vue")['default']
    'ProfilePasswordChangeForm': typeof import("../components/profile/PasswordChangeForm.vue")['default']
    'ProfileInfoForm': typeof import("../components/profile/ProfileInfoForm.vue")['default']
    'TextareaField': typeof import("../components/textarea-field.vue")['default']
    'NuxtWelcome': typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']
    'NuxtLayout': typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']
    'NuxtErrorBoundary': typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']
    'ClientOnly': typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']
    'DevOnly': typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']
    'ServerPlaceholder': typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']
    'NuxtLink': typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']
    'NuxtLoadingIndicator': typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
    'NuxtTime': typeof import("../node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']
    'NuxtRouteAnnouncer': typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
    'NuxtImg': typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']
    'NuxtPicture': typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']
    'Icon': typeof import("../node_modules/@nuxt/icon/dist/runtime/components/index")['default']
    'Button': typeof import("../components/ui/button/index")['Button']
    'Card': typeof import("../components/ui/card/index")['Card']
    'CardAction': typeof import("../components/ui/card/index")['CardAction']
    'CardContent': typeof import("../components/ui/card/index")['CardContent']
    'CardDescription': typeof import("../components/ui/card/index")['CardDescription']
    'CardFooter': typeof import("../components/ui/card/index")['CardFooter']
    'CardHeader': typeof import("../components/ui/card/index")['CardHeader']
    'CardTitle': typeof import("../components/ui/card/index")['CardTitle']
    'NuxtPage': typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']
    'NoScript': typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']
    'Link': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']
    'Base': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']
    'Title': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']
    'Meta': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']
    'Style': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']
    'Head': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']
    'Html': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']
    'Body': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']
    'NuxtIsland': typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']
    'NuxtRouteAnnouncer': IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
      'LazyAppToast': LazyComponent<typeof import("../components/app/toast.vue")['default']>
    'LazyAuthAnimatedBg': LazyComponent<typeof import("../components/auth/animated-bg.vue")['default']>
    'LazyAuthFloatingShapes': LazyComponent<typeof import("../components/auth/floating-shapes.vue")['default']>
    'LazyAuthLoginForm': LazyComponent<typeof import("../components/auth/login/form.vue")['default']>
    'LazyAuthLoginShowcase': LazyComponent<typeof import("../components/auth/login/showcase.vue")['default']>
    'LazyAuthSignupForm': LazyComponent<typeof import("../components/auth/signup/form.vue")['default']>
    'LazyAuthSignupPasswordField': LazyComponent<typeof import("../components/auth/signup/password-field.vue")['default']>
    'LazyAuthSignupPasswordStrengthIndicator': LazyComponent<typeof import("../components/auth/signup/password-strength-indicator.vue")['default']>
    'LazyAuthSignupPhoneField': LazyComponent<typeof import("../components/auth/signup/phone-field.vue")['default']>
    'LazyAuthSignupShowcase': LazyComponent<typeof import("../components/auth/signup/showcase.vue")['default']>
    'LazyAuthSignupSubdomainField': LazyComponent<typeof import("../components/auth/signup/subdomain-field.vue")['default']>
    'LazyInputField': LazyComponent<typeof import("../components/input-field.vue")['default']>
    'LazyInputSelect': LazyComponent<typeof import("../components/input-select.vue")['default']>
    'LazyInputTextarea': LazyComponent<typeof import("../components/input-textarea.vue")['default']>
    'LazyMainHero': LazyComponent<typeof import("../components/main/hero.vue")['default']>
    'LazyProfilePasswordChangeForm': LazyComponent<typeof import("../components/profile/PasswordChangeForm.vue")['default']>
    'LazyProfileInfoForm': LazyComponent<typeof import("../components/profile/ProfileInfoForm.vue")['default']>
    'LazyTextareaField': LazyComponent<typeof import("../components/textarea-field.vue")['default']>
    'LazyNuxtWelcome': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']>
    'LazyNuxtLayout': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']>
    'LazyNuxtErrorBoundary': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']>
    'LazyClientOnly': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']>
    'LazyDevOnly': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']>
    'LazyServerPlaceholder': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
    'LazyNuxtLink': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']>
    'LazyNuxtLoadingIndicator': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']>
    'LazyNuxtTime': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']>
    'LazyNuxtRouteAnnouncer': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']>
    'LazyNuxtImg': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']>
    'LazyNuxtPicture': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']>
    'LazyIcon': LazyComponent<typeof import("../node_modules/@nuxt/icon/dist/runtime/components/index")['default']>
    'LazyButton': LazyComponent<typeof import("../components/ui/button/index")['Button']>
    'LazyCard': LazyComponent<typeof import("../components/ui/card/index")['Card']>
    'LazyCardAction': LazyComponent<typeof import("../components/ui/card/index")['CardAction']>
    'LazyCardContent': LazyComponent<typeof import("../components/ui/card/index")['CardContent']>
    'LazyCardDescription': LazyComponent<typeof import("../components/ui/card/index")['CardDescription']>
    'LazyCardFooter': LazyComponent<typeof import("../components/ui/card/index")['CardFooter']>
    'LazyCardHeader': LazyComponent<typeof import("../components/ui/card/index")['CardHeader']>
    'LazyCardTitle': LazyComponent<typeof import("../components/ui/card/index")['CardTitle']>
    'LazyNuxtPage': LazyComponent<typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']>
    'LazyNoScript': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']>
    'LazyLink': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']>
    'LazyBase': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']>
    'LazyTitle': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']>
    'LazyMeta': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']>
    'LazyStyle': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']>
    'LazyHead': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']>
    'LazyHtml': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']>
    'LazyBody': LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']>
    'LazyNuxtIsland': LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']>
    'LazyNuxtRouteAnnouncer': LazyComponent<IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>>
}

declare module 'vue' {
  export interface GlobalComponents extends _GlobalComponents { }
}

export const AppToast: typeof import("../components/app/toast.vue")['default']
export const AuthAnimatedBg: typeof import("../components/auth/animated-bg.vue")['default']
export const AuthFloatingShapes: typeof import("../components/auth/floating-shapes.vue")['default']
export const AuthLoginForm: typeof import("../components/auth/login/form.vue")['default']
export const AuthLoginShowcase: typeof import("../components/auth/login/showcase.vue")['default']
export const AuthSignupForm: typeof import("../components/auth/signup/form.vue")['default']
export const AuthSignupPasswordField: typeof import("../components/auth/signup/password-field.vue")['default']
export const AuthSignupPasswordStrengthIndicator: typeof import("../components/auth/signup/password-strength-indicator.vue")['default']
export const AuthSignupPhoneField: typeof import("../components/auth/signup/phone-field.vue")['default']
export const AuthSignupShowcase: typeof import("../components/auth/signup/showcase.vue")['default']
export const AuthSignupSubdomainField: typeof import("../components/auth/signup/subdomain-field.vue")['default']
export const InputField: typeof import("../components/input-field.vue")['default']
export const InputSelect: typeof import("../components/input-select.vue")['default']
export const InputTextarea: typeof import("../components/input-textarea.vue")['default']
export const MainHero: typeof import("../components/main/hero.vue")['default']
export const ProfilePasswordChangeForm: typeof import("../components/profile/PasswordChangeForm.vue")['default']
export const ProfileInfoForm: typeof import("../components/profile/ProfileInfoForm.vue")['default']
export const TextareaField: typeof import("../components/textarea-field.vue")['default']
export const NuxtWelcome: typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']
export const NuxtLayout: typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']
export const NuxtErrorBoundary: typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']
export const ClientOnly: typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']
export const DevOnly: typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']
export const ServerPlaceholder: typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']
export const NuxtLink: typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']
export const NuxtLoadingIndicator: typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
export const NuxtTime: typeof import("../node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']
export const NuxtRouteAnnouncer: typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']
export const NuxtImg: typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']
export const NuxtPicture: typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']
export const Icon: typeof import("../node_modules/@nuxt/icon/dist/runtime/components/index")['default']
export const Button: typeof import("../components/ui/button/index")['Button']
export const Card: typeof import("../components/ui/card/index")['Card']
export const CardAction: typeof import("../components/ui/card/index")['CardAction']
export const CardContent: typeof import("../components/ui/card/index")['CardContent']
export const CardDescription: typeof import("../components/ui/card/index")['CardDescription']
export const CardFooter: typeof import("../components/ui/card/index")['CardFooter']
export const CardHeader: typeof import("../components/ui/card/index")['CardHeader']
export const CardTitle: typeof import("../components/ui/card/index")['CardTitle']
export const NuxtPage: typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']
export const NoScript: typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']
export const Link: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']
export const Base: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']
export const Title: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']
export const Meta: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']
export const Style: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']
export const Head: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']
export const Html: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']
export const Body: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']
export const NuxtIsland: typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']
export const NuxtRouteAnnouncer: IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
export const LazyAppToast: LazyComponent<typeof import("../components/app/toast.vue")['default']>
export const LazyAuthAnimatedBg: LazyComponent<typeof import("../components/auth/animated-bg.vue")['default']>
export const LazyAuthFloatingShapes: LazyComponent<typeof import("../components/auth/floating-shapes.vue")['default']>
export const LazyAuthLoginForm: LazyComponent<typeof import("../components/auth/login/form.vue")['default']>
export const LazyAuthLoginShowcase: LazyComponent<typeof import("../components/auth/login/showcase.vue")['default']>
export const LazyAuthSignupForm: LazyComponent<typeof import("../components/auth/signup/form.vue")['default']>
export const LazyAuthSignupPasswordField: LazyComponent<typeof import("../components/auth/signup/password-field.vue")['default']>
export const LazyAuthSignupPasswordStrengthIndicator: LazyComponent<typeof import("../components/auth/signup/password-strength-indicator.vue")['default']>
export const LazyAuthSignupPhoneField: LazyComponent<typeof import("../components/auth/signup/phone-field.vue")['default']>
export const LazyAuthSignupShowcase: LazyComponent<typeof import("../components/auth/signup/showcase.vue")['default']>
export const LazyAuthSignupSubdomainField: LazyComponent<typeof import("../components/auth/signup/subdomain-field.vue")['default']>
export const LazyInputField: LazyComponent<typeof import("../components/input-field.vue")['default']>
export const LazyInputSelect: LazyComponent<typeof import("../components/input-select.vue")['default']>
export const LazyInputTextarea: LazyComponent<typeof import("../components/input-textarea.vue")['default']>
export const LazyMainHero: LazyComponent<typeof import("../components/main/hero.vue")['default']>
export const LazyProfilePasswordChangeForm: LazyComponent<typeof import("../components/profile/PasswordChangeForm.vue")['default']>
export const LazyProfileInfoForm: LazyComponent<typeof import("../components/profile/ProfileInfoForm.vue")['default']>
export const LazyTextareaField: LazyComponent<typeof import("../components/textarea-field.vue")['default']>
export const LazyNuxtWelcome: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/welcome.vue")['default']>
export const LazyNuxtLayout: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-layout")['default']>
export const LazyNuxtErrorBoundary: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue")['default']>
export const LazyClientOnly: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']>
export const LazyDevOnly: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']>
export const LazyServerPlaceholder: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>
export const LazyNuxtLink: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']>
export const LazyNuxtLoadingIndicator: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']>
export const LazyNuxtTime: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-time.vue")['default']>
export const LazyNuxtRouteAnnouncer: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-route-announcer")['default']>
export const LazyNuxtImg: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtImg']>
export const LazyNuxtPicture: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-stubs")['NuxtPicture']>
export const LazyIcon: LazyComponent<typeof import("../node_modules/@nuxt/icon/dist/runtime/components/index")['default']>
export const LazyButton: LazyComponent<typeof import("../components/ui/button/index")['Button']>
export const LazyCard: LazyComponent<typeof import("../components/ui/card/index")['Card']>
export const LazyCardAction: LazyComponent<typeof import("../components/ui/card/index")['CardAction']>
export const LazyCardContent: LazyComponent<typeof import("../components/ui/card/index")['CardContent']>
export const LazyCardDescription: LazyComponent<typeof import("../components/ui/card/index")['CardDescription']>
export const LazyCardFooter: LazyComponent<typeof import("../components/ui/card/index")['CardFooter']>
export const LazyCardHeader: LazyComponent<typeof import("../components/ui/card/index")['CardHeader']>
export const LazyCardTitle: LazyComponent<typeof import("../components/ui/card/index")['CardTitle']>
export const LazyNuxtPage: LazyComponent<typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']>
export const LazyNoScript: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']>
export const LazyLink: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']>
export const LazyBase: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']>
export const LazyTitle: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']>
export const LazyMeta: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']>
export const LazyStyle: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']>
export const LazyHead: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']>
export const LazyHtml: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']>
export const LazyBody: LazyComponent<typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']>
export const LazyNuxtIsland: LazyComponent<typeof import("../node_modules/nuxt/dist/app/components/nuxt-island")['default']>
export const LazyNuxtRouteAnnouncer: LazyComponent<IslandComponent<typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']>>

export const componentNames: string[]
