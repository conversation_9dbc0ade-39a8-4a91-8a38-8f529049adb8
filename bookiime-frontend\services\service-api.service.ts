// Service API Service following Single Responsibility Principle
import { BaseApiService } from './base-api.service';
import type { 
  Service, 
  CreateServiceRequest, 
  UpdateServiceRequest,
  ServiceSearchParams 
} from '@/types/service.types';
import type { ApiResponse, PaginatedResponse } from '@/types/api.types';

export interface IServiceApiService {
  getServices(params?: ServiceSearchParams): Promise<ApiResponse<Service[]>>;
  getServiceById(id: string): Promise<ApiResponse<Service>>;
  createService(service: CreateServiceRequest): Promise<ApiResponse<Service>>;
  updateService(id: string, service: CreateServiceRequest): Promise<ApiResponse<Service>>;
  deleteService(id: string): Promise<ApiResponse<void>>;
  getServicesByTenant(tenantId: string): Promise<ApiResponse<Service[]>>;
}

export class ServiceApiService extends BaseApiService implements IServiceApiService {
  private readonly endpoint = '/services';

  async getServices(params?: ServiceSearchParams): Promise<ApiResponse<Service[]>> {
    const queryParams = new URLSearchParams();
    
    if (params?.search) {
      queryParams.append('search', params.search);
    }
    if (params?.tenantId) {
      queryParams.append('tenantId', params.tenantId);
    }
    if (params?.page) {
      queryParams.append('page', params.page.toString());
    }
    if (params?.limit) {
      queryParams.append('limit', params.limit.toString());
    }

    const query = queryParams.toString();
    const url = query ? `${this.endpoint}?${query}` : this.endpoint;
    
    return this.get<Service[]>(url);
  }

  async getServiceById(id: string): Promise<ApiResponse<Service>> {
    return this.get<Service>(`${this.endpoint}/${id}`);
  }

  async createService(service: CreateServiceRequest): Promise<ApiResponse<Service>> {
    return this.post<Service>(this.endpoint, service);
  }

  async updateService(id: string, service: CreateServiceRequest): Promise<ApiResponse<Service>> {
    return this.put<Service>(`${this.endpoint}/${id}`, service);
  }

  async deleteService(id: string): Promise<ApiResponse<void>> {
    return this.delete<void>(`${this.endpoint}/${id}`);
  }

  async getServicesByTenant(tenantId: string): Promise<ApiResponse<Service[]>> {
    return this.get<Service[]>(`${this.endpoint}/tenant?tenantId=${tenantId}`);
  }

  // Additional utility methods
  async searchServices(query: string): Promise<ApiResponse<Service[]>> {
    return this.getServices({ search: query });
  }

  async duplicateService(id: string): Promise<ApiResponse<Service>> {
    const serviceResponse = await this.getServiceById(id);
    if (!serviceResponse.success) {
      throw new Error(serviceResponse.message || 'Failed to fetch service for duplication');
    }

    const service = serviceResponse.data;
    const duplicatedService: CreateServiceRequest = {
      name: `${service.name} (Copy)`,
      description: service.description,
      price: service.price,
      duration: service.duration,
    };

    return this.createService(duplicatedService);
  }
}
