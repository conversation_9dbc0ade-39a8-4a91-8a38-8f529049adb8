<template>
  <div class="p-6">
    <!-- <PERSON> Header -->
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-2xl font-bold text-neutral-900">Bookings</h1>
        <p class="text-neutral-600 mt-1">Manage all your appointments and bookings</p>
      </div>
      <NuxtLink
        to="/dashboard/bookings/new"
        class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
      >
        <Icon name="lucide:plus" class="w-4 h-4 mr-2" />
        New Booking
      </NuxtLink>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg border border-neutral-200 p-6 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-neutral-700 mb-2">Status</label>
          <select class="w-full border border-neutral-300 rounded-lg px-3 py-2">
            <option value="">All Statuses</option>
            <option value="confirmed">Confirmed</option>
            <option value="pending">Pending</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-neutral-700 mb-2">Date Range</label>
          <select class="w-full border border-neutral-300 rounded-lg px-3 py-2">
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="custom">Custom Range</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-neutral-700 mb-2">Service</label>
          <select class="w-full border border-neutral-300 rounded-lg px-3 py-2">
            <option value="">All Services</option>
            <option value="haircut">Hair Cut</option>
            <option value="massage">Massage</option>
            <option value="consultation">Consultation</option>
          </select>
        </div>
        <div class="flex items-end">
          <button class="w-full px-4 py-2 bg-neutral-100 text-neutral-700 rounded-lg hover:bg-neutral-200 transition-colors">
            Apply Filters
          </button>
        </div>
      </div>
    </div>

    <!-- Bookings Table -->
    <div class="bg-white rounded-lg border border-neutral-200 overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-neutral-200">
          <thead class="bg-neutral-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                Customer
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                Service
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                Date & Time
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                Status
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-neutral-200">
            <tr v-for="booking in bookings" :key="booking.id">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="w-10 h-10 bg-neutral-100 rounded-full flex items-center justify-center">
                    <Icon name="lucide:user" class="w-5 h-5 text-neutral-600" />
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-neutral-900">{{ booking.customerName }}</div>
                    <div class="text-sm text-neutral-500">{{ booking.customerEmail }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-neutral-900">{{ booking.service }}</div>
                <div class="text-sm text-neutral-500">${{ booking.price }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-neutral-900">{{ booking.date }}</div>
                <div class="text-sm text-neutral-500">{{ booking.time }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                  :class="{
                    'bg-green-100 text-green-800': booking.status === 'confirmed',
                    'bg-yellow-100 text-yellow-800': booking.status === 'pending',
                    'bg-red-100 text-red-800': booking.status === 'cancelled'
                  }"
                >
                  {{ booking.status }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                  <button class="text-primary-600 hover:text-primary-900">Edit</button>
                  <button class="text-red-600 hover:text-red-900">Cancel</button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="bookings.length === 0" class="text-center py-12">
      <Icon name="lucide:calendar-x" class="w-16 h-16 text-neutral-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-neutral-900 mb-2">No bookings found</h3>
      <p class="text-neutral-600 mb-6">Get started by creating your first booking.</p>
      <NuxtLink
        to="/dashboard/bookings/new"
        class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
      >
        <Icon name="lucide:plus" class="w-4 h-4 mr-2" />
        Create Booking
      </NuxtLink>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'dashboard'
})

// Meta tags
useHead({
  title: 'Bookings - Bookiime',
  meta: [
    { name: 'description', content: 'Manage your bookings and appointments' }
  ]
})

// Mock data - replace with actual API calls
const bookings = ref([
  {
    id: 1,
    customerName: 'John Doe',
    customerEmail: '<EMAIL>',
    service: 'Hair Cut',
    price: 50,
    date: 'Dec 18, 2024',
    time: '2:00 PM',
    status: 'confirmed'
  },
  {
    id: 2,
    customerName: 'Jane Smith',
    customerEmail: '<EMAIL>',
    service: 'Massage Therapy',
    price: 80,
    date: 'Dec 19, 2024',
    time: '10:00 AM',
    status: 'pending'
  },
  {
    id: 3,
    customerName: 'Mike Johnson',
    customerEmail: '<EMAIL>',
    service: 'Consultation',
    price: 30,
    date: 'Dec 20, 2024',
    time: '3:30 PM',
    status: 'confirmed'
  }
])

// Load bookings data
onMounted(async () => {
  try {
    // Replace with actual API call
    // const data = await $apiFetch('/dashboard/bookings')
    // bookings.value = data
  } catch (error) {
    console.error('Error loading bookings:', error)
  }
})
</script>
