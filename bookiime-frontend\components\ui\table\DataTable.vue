<template>
  <div class="data-table">
    <!-- Table Header Actions -->
    <div v-if="$slots.header" class="mb-4">
      <slot name="header" />
    </div>

    <!-- Search and Filters -->
    <div v-if="searchable || $slots.filters" class="mb-4 flex flex-col sm:flex-row gap-4">
      <!-- Search -->
      <div v-if="searchable" class="flex-1">
        <FormField
          v-model="searchQuery"
          type="text"
          :placeholder="searchPlaceholder"
          @input="handleSearch"
        >
          <template #prefix>
            <Icon name="lucide:search" class="w-4 h-4" />
          </template>
        </FormField>
      </div>

      <!-- Custom Filters -->
      <div v-if="$slots.filters" class="flex gap-2">
        <slot name="filters" />
      </div>
    </div>

    <!-- Table Container -->
    <div class="overflow-hidden rounded-lg border border-neutral-200 bg-white">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-neutral-200">
          <!-- Table Head -->
          <thead class="bg-neutral-50">
            <tr>
              <th
                v-for="column in columns"
                :key="column.key"
                :class="getHeaderClasses(column)"
                @click="handleSort(column)"
              >
                <div class="flex items-center space-x-2">
                  <span>{{ column.label }}</span>
                  <div v-if="column.sortable" class="flex flex-col">
                    <Icon
                      name="lucide:chevron-up"
                      class="w-3 h-3"
                      :class="{
                        'text-primary-600': sortBy === column.key && sortOrder === 'asc',
                        'text-neutral-400': !(sortBy === column.key && sortOrder === 'asc'),
                      }"
                    />
                    <Icon
                      name="lucide:chevron-down"
                      class="w-3 h-3 -mt-1"
                      :class="{
                        'text-primary-600': sortBy === column.key && sortOrder === 'desc',
                        'text-neutral-400': !(sortBy === column.key && sortOrder === 'desc'),
                      }"
                    />
                  </div>
                </div>
              </th>
              <th
                v-if="$slots.actions"
                class="px-6 py-3 text-right text-xs font-medium text-neutral-500 uppercase tracking-wider"
              >
                Actions
              </th>
            </tr>
          </thead>

          <!-- Table Body -->
          <tbody class="bg-white divide-y divide-neutral-200">
            <!-- Loading State -->
            <tr v-if="loading">
              <td :colspan="columns.length + ($slots.actions ? 1 : 0)" class="px-6 py-12 text-center">
                <div class="flex items-center justify-center space-x-3">
                  <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
                  <span class="text-neutral-500">{{ loadingText }}</span>
                </div>
              </td>
            </tr>

            <!-- Error State -->
            <tr v-else-if="error">
              <td :colspan="columns.length + ($slots.actions ? 1 : 0)" class="px-6 py-12 text-center">
                <div class="flex flex-col items-center space-y-3">
                  <Icon name="lucide:alert-circle" class="w-8 h-8 text-red-500" />
                  <span class="text-red-600">{{ error }}</span>
                  <button
                    v-if="onRetry"
                    @click="onRetry"
                    class="text-primary-600 hover:text-primary-700 font-medium"
                  >
                    Try Again
                  </button>
                </div>
              </td>
            </tr>

            <!-- Empty State -->
            <tr v-else-if="filteredData.length === 0">
              <td :colspan="columns.length + ($slots.actions ? 1 : 0)" class="px-6 py-12 text-center">
                <div class="flex flex-col items-center space-y-3">
                  <Icon name="lucide:inbox" class="w-8 h-8 text-neutral-400" />
                  <span class="text-neutral-500">{{ emptyText }}</span>
                </div>
              </td>
            </tr>

            <!-- Data Rows -->
            <tr
              v-else
              v-for="(item, index) in paginatedData"
              :key="getRowKey(item, index)"
              class="hover:bg-neutral-50 transition-colors"
              :class="{ 'bg-primary-50': selectedItems.includes(getRowKey(item, index)) }"
            >
              <td
                v-for="column in columns"
                :key="column.key"
                :class="getCellClasses(column)"
              >
                <slot
                  :name="`cell-${column.key}`"
                  :item="item"
                  :value="getNestedValue(item, column.key)"
                  :index="index"
                >
                  {{ formatCellValue(item, column) }}
                </slot>
              </td>
              
              <td v-if="$slots.actions" class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <slot name="actions" :item="item" :index="index" />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Pagination -->
    <div v-if="paginated && totalPages > 1" class="mt-4 flex items-center justify-between">
      <div class="text-sm text-neutral-700">
        Showing {{ startIndex + 1 }} to {{ endIndex }} of {{ totalItems }} results
      </div>
      
      <div class="flex items-center space-x-2">
        <button
          @click="goToPage(currentPage - 1)"
          :disabled="currentPage === 1"
          class="px-3 py-2 text-sm font-medium text-neutral-500 bg-white border border-neutral-300 rounded-md hover:bg-neutral-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Previous
        </button>
        
        <span class="px-3 py-2 text-sm font-medium text-neutral-700">
          Page {{ currentPage }} of {{ totalPages }}
        </span>
        
        <button
          @click="goToPage(currentPage + 1)"
          :disabled="currentPage === totalPages"
          class="px-3 py-2 text-sm font-medium text-neutral-500 bg-white border border-neutral-300 rounded-md hover:bg-neutral-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Next
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Column {
  key: string;
  label: string;
  sortable?: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
  formatter?: (value: any, item: any) => string;
}

interface Props {
  data: any[];
  columns: Column[];
  loading?: boolean;
  error?: string;
  searchable?: boolean;
  searchPlaceholder?: string;
  paginated?: boolean;
  pageSize?: number;
  emptyText?: string;
  loadingText?: string;
  rowKey?: string;
  selectedItems?: string[];
  onRetry?: () => void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  searchable: false,
  searchPlaceholder: 'Search...',
  paginated: true,
  pageSize: 10,
  emptyText: 'No data available',
  loadingText: 'Loading...',
  rowKey: 'id',
  selectedItems: () => [],
});

const emit = defineEmits<{
  'sort': [{ column: string; order: 'asc' | 'desc' }];
  'search': [query: string];
  'page-change': [page: number];
}>();

// Reactive state
const searchQuery = ref('');
const sortBy = ref<string>('');
const sortOrder = ref<'asc' | 'desc'>('asc');
const currentPage = ref(1);

// Computed properties
const filteredData = computed(() => {
  let result = [...props.data];

  // Apply search filter
  if (searchQuery.value && props.searchable) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(item =>
      props.columns.some(column =>
        String(getNestedValue(item, column.key) || '').toLowerCase().includes(query)
      )
    );
  }

  // Apply sorting
  if (sortBy.value) {
    result.sort((a, b) => {
      const aVal = getNestedValue(a, sortBy.value);
      const bVal = getNestedValue(b, sortBy.value);
      
      if (aVal < bVal) return sortOrder.value === 'asc' ? -1 : 1;
      if (aVal > bVal) return sortOrder.value === 'asc' ? 1 : -1;
      return 0;
    });
  }

  return result;
});

const totalItems = computed(() => filteredData.value.length);
const totalPages = computed(() => Math.ceil(totalItems.value / props.pageSize));
const startIndex = computed(() => (currentPage.value - 1) * props.pageSize);
const endIndex = computed(() => Math.min(startIndex.value + props.pageSize, totalItems.value));

const paginatedData = computed(() => {
  if (!props.paginated) return filteredData.value;
  return filteredData.value.slice(startIndex.value, startIndex.value + props.pageSize);
});

// Methods
const getNestedValue = (obj: any, path: string) => {
  return path.split('.').reduce((current, key) => current?.[key], obj);
};

const getRowKey = (item: any, index: number) => {
  return getNestedValue(item, props.rowKey) || index;
};

const formatCellValue = (item: any, column: Column) => {
  const value = getNestedValue(item, column.key);
  return column.formatter ? column.formatter(value, item) : value;
};

const getHeaderClasses = (column: Column) => [
  'px-6 py-3 text-xs font-medium text-neutral-500 uppercase tracking-wider',
  `text-${column.align || 'left'}`,
  { 'cursor-pointer hover:bg-neutral-100': column.sortable },
];

const getCellClasses = (column: Column) => [
  'px-6 py-4 whitespace-nowrap text-sm text-neutral-900',
  `text-${column.align || 'left'}`,
  { [`w-${column.width}`]: column.width },
];

const handleSort = (column: Column) => {
  if (!column.sortable) return;

  if (sortBy.value === column.key) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortBy.value = column.key;
    sortOrder.value = 'asc';
  }

  emit('sort', { column: column.key, order: sortOrder.value });
};

const handleSearch = useDebounceFn(() => {
  currentPage.value = 1;
  emit('search', searchQuery.value);
}, 300);

const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
    emit('page-change', page);
  }
};

// Reset page when data changes
watch(() => props.data, () => {
  currentPage.value = 1;
});
</script>
