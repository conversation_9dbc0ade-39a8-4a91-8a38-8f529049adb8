// Add the other types from the Swagger here
export type User = {
  id: string;
  name: string;
  email: string;
  access_token: string;
  subdomain?: string;
  phone?: string;
};

export type phoneNumber = {
  phoneCountryCode: string;
  phoneNumber: string;
  fullNumber: string;
};

export type RegistrationForm = {
  email: string
  password: string
  subdomain: string
  phoneCountryCode: string
  phoneNumber: string
}

// Profile-related types
export interface UserProfile {
  id: string;
  name: string;
  email: string;
  phone?: string;
  subdomain?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface UpdateProfileRequest {
  name: string;
  email: string;
  phone?: string;
  subdomain?: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}


