// Profile API Service following Single Responsibility Principle
import { BaseApiService } from './base-api.service';
import type { 
  UserProfile, 
  UpdateProfileRequest, 
  ChangePasswordRequest 
} from '@/types/user.types';
import type { ApiResponse } from '@/types/api.types';

export interface IProfileApiService {
  getProfile(): Promise<ApiResponse<UserProfile>>;
  updateProfile(data: UpdateProfileRequest): Promise<ApiResponse<UserProfile>>;
  changePassword(data: ChangePasswordRequest): Promise<ApiResponse<void>>;
}

export class ProfileApiService extends BaseApiService implements IProfileApiService {
  private readonly endpoint = '/profile';

  /**
   * Get current user profile
   * GET /profile
   */
  async getProfile(): Promise<ApiResponse<UserProfile>> {
    return this.get<UserProfile>(this.endpoint);
  }

  /**
   * Update user profile information
   * PUT /profile
   */
  async updateProfile(data: UpdateProfileRequest): Promise<ApiResponse<UserProfile>> {
    // Validate required fields
    if (!data.name?.trim()) {
      throw new Error('Name is required');
    }
    if (!data.email?.trim()) {
      throw new Error('Email is required');
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
      throw new Error('Please enter a valid email address');
    }

    // Phone validation (if provided)
    if (data.phone && data.phone.trim()) {
      const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
      if (!phoneRegex.test(data.phone)) {
        throw new Error('Please enter a valid phone number');
      }
    }

    // Subdomain validation (if provided)
    if (data.subdomain && data.subdomain.trim()) {
      const subdomainRegex = /^[a-z0-9]([a-z0-9\-]{0,61}[a-z0-9])?$/;
      if (!subdomainRegex.test(data.subdomain)) {
        throw new Error('Subdomain must contain only lowercase letters, numbers, and hyphens');
      }
    }

    return this.put<UserProfile>(this.endpoint, data);
  }

  /**
   * Change user password
   * PUT /profile/password
   */
  async changePassword(data: ChangePasswordRequest): Promise<ApiResponse<void>> {
    // Validate required fields
    if (!data.currentPassword?.trim()) {
      throw new Error('Current password is required');
    }
    if (!data.newPassword?.trim()) {
      throw new Error('New password is required');
    }
    if (!data.confirmPassword?.trim()) {
      throw new Error('Password confirmation is required');
    }

    // Password match validation
    if (data.newPassword !== data.confirmPassword) {
      throw new Error('New password and confirmation do not match');
    }

    // Password strength validation
    if (data.newPassword.length < 8) {
      throw new Error('New password must be at least 8 characters long');
    }

    // Check if new password is different from current
    if (data.currentPassword === data.newPassword) {
      throw new Error('New password must be different from current password');
    }

    // Password complexity validation
    const hasUpperCase = /[A-Z]/.test(data.newPassword);
    const hasLowerCase = /[a-z]/.test(data.newPassword);
    const hasNumbers = /\d/.test(data.newPassword);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(data.newPassword);

    if (!hasUpperCase || !hasLowerCase || !hasNumbers || !hasSpecialChar) {
      throw new Error('New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character');
    }

    return this.put<void>(`${this.endpoint}/password`, {
      currentPassword: data.currentPassword,
      newPassword: data.newPassword,
    });
  }

  /**
   * Validate email availability (utility method)
   */
  async validateEmailAvailability(email: string): Promise<ApiResponse<{ available: boolean }>> {
    return this.post<{ available: boolean }>(`${this.endpoint}/validate-email`, { email });
  }

  /**
   * Validate subdomain availability (utility method)
   */
  async validateSubdomainAvailability(subdomain: string): Promise<ApiResponse<{ available: boolean }>> {
    return this.post<{ available: boolean }>(`${this.endpoint}/validate-subdomain`, { subdomain });
  }
}
