<template>
  <div class="form-field" :class="fieldClasses">
    <!-- Label -->
    <label
      v-if="label"
      :for="fieldId"
      class="block text-sm font-medium text-neutral-700 mb-2 transition-colors"
      :class="{ 'text-primary-600': isFocused, 'text-red-600': hasError }"
    >
      {{ label }}
      <span v-if="required" class="text-red-500 ml-1">*</span>
    </label>

    <!-- Input Container -->
    <div class="relative">
      <!-- Prefix Slot -->
      <div
        v-if="$slots.prefix"
        class="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 z-10"
      >
        <slot name="prefix" />
      </div>

      <!-- Input Element -->
      <component
        :is="inputComponent"
        :id="fieldId"
        v-model="internalValue"
        :type="type"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :required="required"
        :maxlength="maxlength"
        :min="min"
        :max="max"
        :step="step"
        :rows="rows"
        :class="inputClasses"
        @focus="handleFocus"
        @blur="handleBlur"
        @input="handleInput"
        v-bind="$attrs"
      />

      <!-- Suffix Slot -->
      <div
        v-if="$slots.suffix"
        class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400 z-10"
      >
        <slot name="suffix" />
      </div>

      <!-- Loading Indicator -->
      <div
        v-if="loading"
        class="absolute right-3 top-1/2 transform -translate-y-1/2"
      >
        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600"></div>
      </div>
    </div>

    <!-- Help Text -->
    <p
      v-if="helpText && !hasError"
      class="mt-2 text-sm text-neutral-500"
    >
      {{ helpText }}
    </p>

    <!-- Error Message -->
    <p
      v-if="hasError"
      class="mt-2 text-sm text-red-600 flex items-center"
    >
      <Icon name="lucide:alert-circle" class="w-4 h-4 mr-1 flex-shrink-0" />
      {{ errorMessage }}
    </p>

    <!-- Character Count -->
    <p
      v-if="showCharCount && maxlength"
      class="mt-1 text-xs text-neutral-400 text-right"
    >
      {{ characterCount }}/{{ maxlength }}
    </p>
  </div>
</template>

<script setup lang="ts">
interface Props {
  modelValue: string | number;
  label?: string;
  type?: string;
  placeholder?: string;
  helpText?: string;
  errorMessage?: string;
  required?: boolean;
  disabled?: boolean;
  readonly?: boolean;
  loading?: boolean;
  maxlength?: number;
  min?: number;
  max?: number;
  step?: number;
  rows?: number;
  showCharCount?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'filled' | 'outlined';
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  size: 'md',
  variant: 'default',
  rows: 3,
});

const emit = defineEmits<{
  'update:modelValue': [value: string | number];
  'focus': [event: FocusEvent];
  'blur': [event: FocusEvent];
  'input': [event: Event];
}>();

const fieldId = computed(() => `field-${Math.random().toString(36).substr(2, 9)}`);
const isFocused = ref(false);
const hasError = computed(() => !!props.errorMessage);

const internalValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const characterCount = computed(() => {
  return String(props.modelValue || '').length;
});

const inputComponent = computed(() => {
  return props.type === 'textarea' ? 'textarea' : 'input';
});

const fieldClasses = computed(() => [
  'form-field',
  `form-field--${props.size}`,
  `form-field--${props.variant}`,
  {
    'form-field--focused': isFocused.value,
    'form-field--error': hasError.value,
    'form-field--disabled': props.disabled,
  },
]);

const inputClasses = computed(() => {
  const baseClasses = [
    'w-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
  ];

  // Size classes
  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-3 text-base',
    lg: 'px-5 py-4 text-lg',
  };

  // Variant classes
  const variantClasses = {
    default: 'border border-neutral-300 rounded-lg bg-white',
    filled: 'border-0 rounded-lg bg-neutral-100 focus:bg-white',
    outlined: 'border-2 border-neutral-300 rounded-lg bg-transparent',
  };

  // State classes
  const stateClasses = [];
  if (hasError.value) {
    stateClasses.push('border-red-500 focus:border-red-500 focus:ring-red-500');
  }
  if (props.disabled) {
    stateClasses.push('bg-neutral-100 text-neutral-500 cursor-not-allowed');
  }

  // Padding adjustments for prefix/suffix
  const paddingClasses = [];
  if (props.$slots?.prefix) {
    paddingClasses.push('pl-10');
  }
  if (props.$slots?.suffix || props.loading) {
    paddingClasses.push('pr-10');
  }

  return [
    ...baseClasses,
    sizeClasses[props.size],
    variantClasses[props.variant],
    ...stateClasses,
    ...paddingClasses,
  ];
});

const handleFocus = (event: FocusEvent) => {
  isFocused.value = true;
  emit('focus', event);
};

const handleBlur = (event: FocusEvent) => {
  isFocused.value = false;
  emit('blur', event);
};

const handleInput = (event: Event) => {
  emit('input', event);
};
</script>

<style scoped>
.form-field--disabled {
  opacity: 0.6;
}
</style>
