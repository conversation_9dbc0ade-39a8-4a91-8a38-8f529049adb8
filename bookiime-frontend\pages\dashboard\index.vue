<template>
  <div class="min-h-screen bg-gradient-to-br from-background via-neutral-50 to-neutral-100">
    <!-- Header -->
    <header class="bg-white/80 backdrop-blur-xl shadow-sm border-b border-neutral-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center space-x-4">
            <div class="w-8 h-8 primary-background rounded-lg flex items-center justify-center">
              <img src="/favicon.png" alt="Bookiime" class="w-5 h-5 object-contain" />
            </div>
            <h1 class="text-xl font-semibold text-neutral-900">Dashboard</h1>
          </div>
          <div class="flex items-center space-x-4">
            <div class="text-right">
              <p class="text-sm font-medium text-neutral-900">{{ user?.name || user?.email?.split('@')[0] }}</p>
              <p class="text-xs text-neutral-600">{{ user?.email }}</p>
            </div>
            <Button
              @click="handleLogout"
              variant="outline"
              size="sm"
              class="text-red-600 border-red-200 hover:bg-red-50"
            >
              <Icon name="lucide:log-out" class="w-4 h-4 mr-2" />
              Logout
            </Button>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <!-- Welcome Section -->
      <div class="mb-8">
        <div class="bg-white rounded-2xl shadow-lg border border-neutral-200 p-8">
          <div class="text-center">
            <div class="inline-flex items-center justify-center w-20 h-20 primary-background rounded-2xl mb-6">
              <Icon name="lucide:calendar-check" class="w-10 h-10 text-white" />
            </div>
            <h2 class="text-3xl font-bold text-neutral-900 mb-4">Welcome to Bookiime!</h2>
            <p class="text-lg text-neutral-600 mb-6">Your intelligent scheduling platform is ready to use.</p>

            <!-- User Info Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
              <div class="bg-gradient-to-br from-primary-50 to-primary-100 rounded-xl p-4">
                <div class="flex items-center space-x-3">
                  <Icon name="lucide:mail" class="w-5 h-5 text-primary-600" />
                  <div class="text-left">
                    <p class="text-sm text-primary-600 font-medium">Email</p>
                    <p class="text-sm text-primary-800">{{ user?.email }}</p>
                  </div>
                </div>
              </div>

              <div v-if="user?.subdomain" class="bg-gradient-to-br from-secondary-50 to-secondary-100 rounded-xl p-4">
                <div class="flex items-center space-x-3">
                  <Icon name="lucide:globe" class="w-5 h-5 text-secondary-600" />
                  <div class="text-left">
                    <p class="text-sm text-secondary-600 font-medium">Subdomain</p>
                    <p class="text-sm text-secondary-800">{{ user.subdomain }}.bookiime.com</p>
                  </div>
                </div>
              </div>

              <div v-if="user?.phone" class="bg-gradient-to-br from-tertiary-50 to-tertiary-100 rounded-xl p-4">
                <div class="flex items-center space-x-3">
                  <Icon name="lucide:phone" class="w-5 h-5 text-tertiary-600" />
                  <div class="text-left">
                    <p class="text-sm text-tertiary-600 font-medium">Phone</p>
                    <p class="text-sm text-tertiary-800">{{ user.phone }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card class="hover:shadow-xl transition-shadow duration-300">
          <CardHeader>
            <div class="flex items-center space-x-3">
              <div class="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center">
                <Icon name="lucide:calendar-plus" class="w-6 h-6 text-primary-600" />
              </div>
              <div>
                <CardTitle>Create Booking</CardTitle>
                <CardDescription>Set up your first appointment</CardDescription>
              </div>
            </div>
          </CardHeader>
        </Card>

        <Card class="hover:shadow-xl transition-shadow duration-300">
          <CardHeader>
            <div class="flex items-center space-x-3">
              <div class="w-12 h-12 bg-secondary-100 rounded-xl flex items-center justify-center">
                <Icon name="lucide:settings" class="w-6 h-6 text-secondary-600" />
              </div>
              <div>
                <CardTitle>Settings</CardTitle>
                <CardDescription>Configure your preferences</CardDescription>
              </div>
            </div>
          </CardHeader>
        </Card>

        <Card class="hover:shadow-xl transition-shadow duration-300">
          <CardHeader>
            <div class="flex items-center space-x-3">
              <div class="w-12 h-12 bg-tertiary-100 rounded-xl flex items-center justify-center">
                <Icon name="lucide:users" class="w-6 h-6 text-tertiary-600" />
              </div>
              <div>
                <CardTitle>Clients</CardTitle>
                <CardDescription>Manage your client list</CardDescription>
              </div>
            </div>
          </CardHeader>
        </Card>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'dashboard'
})

// Get user data and auth functions
const { user } = useUser()
const { $logout } = useNuxtApp()

// Handle logout
const handleLogout = async () => {
  try {
    await $logout()
  } catch (error) {
    console.error('Logout error:', error)
  }
}
</script>