<template>
  <Card>
    <CardHeader>
      <CardTitle>Change Password</CardTitle>
      <CardDescription>
        Update your password to keep your account secure
      </CardDescription>
    </CardHeader>
    
    <CardContent>
      <form @submit.prevent="handleSubmit" class="space-y-6">
        <!-- Current Password -->
        <FormField
          v-model="form.currentPassword"
          label="Current Password"
          :type="showCurrentPassword ? 'text' : 'password'"
          placeholder="Enter your current password"
          required
          :error-message="errors.currentPassword"
          :disabled="loading"
        >
          <template #suffix>
            <button
              type="button"
              @click="showCurrentPassword = !showCurrentPassword"
              class="text-neutral-400 hover:text-neutral-600 transition-colors"
            >
              <Icon
                :name="showCurrentPassword ? 'lucide:eye-off' : 'lucide:eye'"
                class="w-4 h-4"
              />
            </button>
          </template>
        </FormField>

        <!-- New Password -->
        <FormField
          v-model="form.newPassword"
          label="New Password"
          :type="showNewPassword ? 'text' : 'password'"
          placeholder="Enter your new password"
          required
          :error-message="errors.newPassword"
          :disabled="loading"
        >
          <template #suffix>
            <button
              type="button"
              @click="showNewPassword = !showNewPassword"
              class="text-neutral-400 hover:text-neutral-600 transition-colors"
            >
              <Icon
                :name="showNewPassword ? 'lucide:eye-off' : 'lucide:eye'"
                class="w-4 h-4"
              />
            </button>
          </template>
        </FormField>

        <!-- Password Strength Indicator -->
        <div v-if="form.newPassword" class="space-y-2">
          <div class="flex items-center space-x-2">
            <span class="text-sm text-neutral-600">Password strength:</span>
            <div class="flex-1 bg-neutral-200 rounded-full h-2">
              <div
                class="h-2 rounded-full transition-all duration-300"
                :class="passwordStrengthColor"
                :style="{ width: `${passwordStrengthPercentage}%` }"
              ></div>
            </div>
            <span class="text-sm font-medium" :class="passwordStrengthTextColor">
              {{ passwordStrengthText }}
            </span>
          </div>
          
          <!-- Password Requirements -->
          <div class="grid grid-cols-2 gap-2 text-xs">
            <div class="flex items-center space-x-1">
              <Icon
                :name="hasMinLength ? 'lucide:check' : 'lucide:x'"
                :class="hasMinLength ? 'text-green-500' : 'text-red-500'"
                class="w-3 h-3"
              />
              <span :class="hasMinLength ? 'text-green-600' : 'text-red-600'">
                At least 8 characters
              </span>
            </div>
            <div class="flex items-center space-x-1">
              <Icon
                :name="hasUpperCase ? 'lucide:check' : 'lucide:x'"
                :class="hasUpperCase ? 'text-green-500' : 'text-red-500'"
                class="w-3 h-3"
              />
              <span :class="hasUpperCase ? 'text-green-600' : 'text-red-600'">
                Uppercase letter
              </span>
            </div>
            <div class="flex items-center space-x-1">
              <Icon
                :name="hasLowerCase ? 'lucide:check' : 'lucide:x'"
                :class="hasLowerCase ? 'text-green-500' : 'text-red-500'"
                class="w-3 h-3"
              />
              <span :class="hasLowerCase ? 'text-green-600' : 'text-red-600'">
                Lowercase letter
              </span>
            </div>
            <div class="flex items-center space-x-1">
              <Icon
                :name="hasNumber ? 'lucide:check' : 'lucide:x'"
                :class="hasNumber ? 'text-green-500' : 'text-red-500'"
                class="w-3 h-3"
              />
              <span :class="hasNumber ? 'text-green-600' : 'text-red-600'">
                Number
              </span>
            </div>
            <div class="flex items-center space-x-1">
              <Icon
                :name="hasSpecialChar ? 'lucide:check' : 'lucide:x'"
                :class="hasSpecialChar ? 'text-green-500' : 'text-red-500'"
                class="w-3 h-3"
              />
              <span :class="hasSpecialChar ? 'text-green-600' : 'text-red-600'">
                Special character
              </span>
            </div>
          </div>
        </div>

        <!-- Confirm Password -->
        <FormField
          v-model="form.confirmPassword"
          label="Confirm New Password"
          :type="showConfirmPassword ? 'text' : 'password'"
          placeholder="Confirm your new password"
          required
          :error-message="errors.confirmPassword"
          :disabled="loading"
        >
          <template #suffix>
            <button
              type="button"
              @click="showConfirmPassword = !showConfirmPassword"
              class="text-neutral-400 hover:text-neutral-600 transition-colors"
            >
              <Icon
                :name="showConfirmPassword ? 'lucide:eye-off' : 'lucide:eye'"
                class="w-4 h-4"
              />
            </button>
          </template>
        </FormField>

        <!-- Security Notice -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div class="flex items-start space-x-3">
            <Icon name="lucide:shield-check" class="w-5 h-5 text-blue-600 mt-0.5" />
            <div class="text-sm text-blue-800">
              <p class="font-medium mb-1">Security Tips:</p>
              <ul class="space-y-1 text-blue-700">
                <li>• Use a unique password that you don't use elsewhere</li>
                <li>• Consider using a password manager</li>
                <li>• Avoid using personal information in your password</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-end space-x-3 pt-6 border-t border-neutral-200">
          <Button
            type="button"
            variant="outline"
            @click="resetForm"
            :disabled="loading"
          >
            Reset
          </Button>
          <Button
            type="submit"
            :loading="loading"
            :disabled="!isValid"
          >
            Change Password
          </Button>
        </div>
      </form>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import type { ChangePasswordRequest } from '@/types/user.types';

interface Props {
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
});

const emit = defineEmits<{
  'change-password': [data: ChangePasswordRequest];
}>();

// Form state
const form = reactive<ChangePasswordRequest>({
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
});

const errors = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
});

// Password visibility state
const showCurrentPassword = ref(false);
const showNewPassword = ref(false);
const showConfirmPassword = ref(false);

// Password strength validation
const hasMinLength = computed(() => form.newPassword.length >= 8);
const hasUpperCase = computed(() => /[A-Z]/.test(form.newPassword));
const hasLowerCase = computed(() => /[a-z]/.test(form.newPassword));
const hasNumber = computed(() => /\d/.test(form.newPassword));
const hasSpecialChar = computed(() => /[!@#$%^&*(),.?":{}|<>]/.test(form.newPassword));

const passwordStrength = computed(() => {
  const checks = [hasMinLength.value, hasUpperCase.value, hasLowerCase.value, hasNumber.value, hasSpecialChar.value];
  return checks.filter(Boolean).length;
});

const passwordStrengthPercentage = computed(() => (passwordStrength.value / 5) * 100);

const passwordStrengthText = computed(() => {
  switch (passwordStrength.value) {
    case 0:
    case 1: return 'Very Weak';
    case 2: return 'Weak';
    case 3: return 'Fair';
    case 4: return 'Good';
    case 5: return 'Strong';
    default: return 'Very Weak';
  }
});

const passwordStrengthColor = computed(() => {
  switch (passwordStrength.value) {
    case 0:
    case 1: return 'bg-red-500';
    case 2: return 'bg-orange-500';
    case 3: return 'bg-yellow-500';
    case 4: return 'bg-blue-500';
    case 5: return 'bg-green-500';
    default: return 'bg-red-500';
  }
});

const passwordStrengthTextColor = computed(() => {
  switch (passwordStrength.value) {
    case 0:
    case 1: return 'text-red-600';
    case 2: return 'text-orange-600';
    case 3: return 'text-yellow-600';
    case 4: return 'text-blue-600';
    case 5: return 'text-green-600';
    default: return 'text-red-600';
  }
});

const isValid = computed(() => {
  return !Object.values(errors).some(error => error !== '') &&
         form.currentPassword.trim() !== '' &&
         form.newPassword.trim() !== '' &&
         form.confirmPassword.trim() !== '' &&
         passwordStrength.value >= 4;
});

// Methods
const validateForm = (): boolean => {
  // Reset errors
  Object.keys(errors).forEach(key => {
    errors[key as keyof typeof errors] = '';
  });

  let isValid = true;

  // Current password validation
  if (!form.currentPassword.trim()) {
    errors.currentPassword = 'Current password is required';
    isValid = false;
  }

  // New password validation
  if (!form.newPassword.trim()) {
    errors.newPassword = 'New password is required';
    isValid = false;
  } else if (passwordStrength.value < 4) {
    errors.newPassword = 'Password is not strong enough';
    isValid = false;
  } else if (form.currentPassword === form.newPassword) {
    errors.newPassword = 'New password must be different from current password';
    isValid = false;
  }

  // Confirm password validation
  if (!form.confirmPassword.trim()) {
    errors.confirmPassword = 'Password confirmation is required';
    isValid = false;
  } else if (form.newPassword !== form.confirmPassword) {
    errors.confirmPassword = 'Passwords do not match';
    isValid = false;
  }

  return isValid;
};

const resetForm = () => {
  form.currentPassword = '';
  form.newPassword = '';
  form.confirmPassword = '';
  
  // Reset errors
  Object.keys(errors).forEach(key => {
    errors[key as keyof typeof errors] = '';
  });
  
  // Reset password visibility
  showCurrentPassword.value = false;
  showNewPassword.value = false;
  showConfirmPassword.value = false;
};

const handleSubmit = () => {
  if (validateForm()) {
    emit('change-password', { ...form });
  }
};
</script>
