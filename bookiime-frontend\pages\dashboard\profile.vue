<template>
  <div class="min-h-screen bg-neutral-50">
    <!-- <PERSON> Header -->
    <div class="bg-white border-b border-neutral-200 px-6 py-4">
      <div class="max-w-4xl mx-auto">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-neutral-900">Profile Settings</h1>
            <p class="text-neutral-600 mt-1">Manage your account information and security settings</p>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
              <Icon name="lucide:user" class="w-5 h-5 text-primary-600" />
            </div>
            <div class="text-right">
              <p class="text-sm font-medium text-neutral-900">{{ profile?.name || 'User' }}</p>
              <p class="text-xs text-neutral-500">{{ profile?.email }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto px-6 py-8">
      <div class="space-y-8">
        <!-- Profile Information Form -->
        <ProfileInfoForm
          :profile="profile"
          :loading="profileLoading"
          @update:profile="handleUpdateProfile"
          @validate-email="handleValidateEmail"
          @validate-subdomain="handleValidateSubdomain"
        />

        <!-- Password Change Form -->
        <PasswordChangeForm
          :loading="passwordLoading"
          @change-password="handleChangePassword"
        />

        <!-- Account Actions -->
        <Card>
          <CardHeader>
            <CardTitle>Account Actions</CardTitle>
            <CardDescription>
              Additional account management options
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <div class="space-y-4">
              <!-- Export Data -->
              <div class="flex items-center justify-between p-4 border border-neutral-200 rounded-lg">
                <div class="flex items-center space-x-3">
                  <Icon name="lucide:download" class="w-5 h-5 text-neutral-600" />
                  <div>
                    <h3 class="font-medium text-neutral-900">Export Data</h3>
                    <p class="text-sm text-neutral-600">Download a copy of your account data</p>
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  Export
                </Button>
              </div>

              <!-- Delete Account -->
              <div class="flex items-center justify-between p-4 border border-red-200 rounded-lg bg-red-50">
                <div class="flex items-center space-x-3">
                  <Icon name="lucide:trash-2" class="w-5 h-5 text-red-600" />
                  <div>
                    <h3 class="font-medium text-red-900">Delete Account</h3>
                    <p class="text-sm text-red-600">Permanently delete your account and all data</p>
                  </div>
                </div>
                <Button variant="destructive" size="sm" @click="showDeleteModal = true">
                  Delete
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>

    <!-- Delete Account Confirmation Modal -->
    <BaseModal
      v-model="showDeleteModal"
      title="Delete Account"
      size="sm"
    >
      <div class="text-center">
        <Icon name="lucide:alert-triangle" class="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h3 class="text-lg font-semibold text-neutral-900 mb-2">Are you absolutely sure?</h3>
        <p class="text-neutral-600 mb-6">
          This action cannot be undone. This will permanently delete your account and remove all your data from our servers.
        </p>
        
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <p class="text-sm text-red-800">
            <strong>This will delete:</strong>
          </p>
          <ul class="text-sm text-red-700 mt-2 space-y-1">
            <li>• Your profile and account information</li>
            <li>• All your services and bookings</li>
            <li>• Customer data and history</li>
            <li>• Reports and analytics</li>
          </ul>
        </div>

        <FormField
          v-model="deleteConfirmation"
          label="Type 'DELETE' to confirm"
          placeholder="DELETE"
          :error-message="deleteError"
        />
      </div>

      <template #footer>
        <Button variant="outline" @click="closeDeleteModal">
          Cancel
        </Button>
        <Button
          variant="destructive"
          @click="handleDeleteAccount"
          :disabled="deleteConfirmation !== 'DELETE' || deleteLoading"
        >
          <div v-if="deleteLoading" class="flex items-center space-x-2">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            <span>Deleting...</span>
          </div>
          <span v-else>Delete Account</span>
        </Button>
      </template>
    </BaseModal>
  </div>
</template>

<script setup lang="ts">
import type { UserProfile, UpdateProfileRequest, ChangePasswordRequest } from '@/types/user.types';
import { ProfileApiService } from '@/services/profile-api.service';
import ProfileInfoForm from '@/components/profile/ProfileInfoForm.vue';
import PasswordChangeForm from '@/components/profile/PasswordChangeForm.vue';

// Define page metadata
definePageMeta({
  layout: 'dashboard'
});

// SEO
useHead({
  title: 'Profile Settings - Bookiime',
  meta: [
    { name: 'description', content: 'Manage your profile information and account settings' }
  ]
});

// Dependency Injection
const profileApi = new ProfileApiService();
const { user, setUser } = useUser();

// Reactive state
const profile = ref<UserProfile | null>(null);
const profileLoading = ref(false);
const passwordLoading = ref(false);
const deleteLoading = ref(false);
const showDeleteModal = ref(false);
const deleteConfirmation = ref('');
const deleteError = ref('');

// Profile management methods
const fetchProfile = async () => {
  try {
    profileLoading.value = true;
    const response = await profileApi.getProfile();
    if (response.success) {
      profile.value = response.data;
    } else {
      throw new Error(response.message || 'Failed to fetch profile');
    }
  } catch (error) {
    console.error('Error fetching profile:', error);
    $toast('Failed to load profile information', { type: 'error' });
  } finally {
    profileLoading.value = false;
  }
};

const handleUpdateProfile = async (data: UpdateProfileRequest) => {
  try {
    profileLoading.value = true;
    const response = await profileApi.updateProfile(data);
    if (response.success) {
      profile.value = response.data;
      // Update user cookie if email changed
      if (user.value && user.value.email !== response.data.email) {
        setUser({ ...user.value, ...response.data });
      }
      $toast('Profile updated successfully', { type: 'success' });
    } else {
      throw new Error(response.message || 'Failed to update profile');
    }
  } catch (error) {
    console.error('Error updating profile:', error);
    $toast(error instanceof Error ? error.message : 'Failed to update profile', { type: 'error' });
  } finally {
    profileLoading.value = false;
  }
};

const handleChangePassword = async (data: ChangePasswordRequest) => {
  try {
    passwordLoading.value = true;
    const response = await profileApi.changePassword(data);
    if (response.success) {
      $toast('Password changed successfully', { type: 'success' });
    } else {
      throw new Error(response.message || 'Failed to change password');
    }
  } catch (error) {
    console.error('Error changing password:', error);
    $toast(error instanceof Error ? error.message : 'Failed to change password', { type: 'error' });
  } finally {
    passwordLoading.value = false;
  }
};

const handleValidateEmail = async (email: string) => {
  try {
    const response = await profileApi.validateEmailAvailability(email);
    if (!response.data.available) {
      $toast('Email address is already in use', { type: 'warning' });
    }
  } catch (error) {
    console.error('Error validating email:', error);
  }
};

const handleValidateSubdomain = async (subdomain: string) => {
  try {
    const response = await profileApi.validateSubdomainAvailability(subdomain);
    if (!response.data.available) {
      $toast('Subdomain is already taken', { type: 'warning' });
    }
  } catch (error) {
    console.error('Error validating subdomain:', error);
  }
};

const closeDeleteModal = () => {
  showDeleteModal.value = false;
  deleteConfirmation.value = '';
  deleteError.value = '';
};

const handleDeleteAccount = async () => {
  if (deleteConfirmation.value !== 'DELETE') {
    deleteError.value = 'Please type DELETE to confirm';
    return;
  }

  try {
    deleteLoading.value = true;
    // In a real app, you would call an API to delete the account
    // await profileApi.deleteAccount();
    
    // For now, just show a message and redirect
    $toast('Account deletion requested. You will be contacted for confirmation.', { type: 'info' });
    closeDeleteModal();
  } catch (error) {
    console.error('Error deleting account:', error);
    $toast('Failed to delete account', { type: 'error' });
  } finally {
    deleteLoading.value = false;
  }
};

// Lifecycle
onMounted(async () => {
  await fetchProfile();
});
</script>
