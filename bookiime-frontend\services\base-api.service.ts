// Base API Service following Single Responsibility and Dependency Inversion Principles
import type { 
  ApiResponse, 
  IApiService, 
  RequestOptions, 
  ApiConfig,
  HttpMethod 
} from '@/types/api.types';

export class BaseApiService implements IApiService {
  protected config: ApiConfig;

  constructor(config?: Partial<ApiConfig>) {
    const runtimeConfig = useRuntimeConfig();
    this.config = {
      baseUrl: runtimeConfig.public.apiBase || '/api',
      timeout: 30000,
      retries: 3,
      ...config,
    };
  }

  protected async makeRequest<T>(
    url: string, 
    options: RequestOptions = {}
  ): Promise<ApiResponse<T>> {
    const { $auth } = useNuxtApp();
    const fullUrl = `${this.config.baseUrl}${url}`;

    const requestOptions = {
      method: options.method || 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${$auth.value.token}`,
        ...options.headers,
      },
      timeout: options.timeout || this.config.timeout,
      ...options,
    };

    // Handle request body
    if (options.body && options.method !== 'GET') {
      if (options.body instanceof FormData) {
        requestOptions.body = options.body;
      } else {
        requestOptions.headers['Content-Type'] = 'application/json';
        requestOptions.body = JSON.stringify(options.body);
      }
    }

    try {
      const response = await $fetch<ApiResponse<T>>(fullUrl, requestOptions);
      return response;
    } catch (error: any) {
      // Handle authentication errors
      if (error.status === 401) {
        const { $logout } = useNuxtApp();
        await $logout();
      }
      
      // Transform error to consistent format
      throw this.transformError(error);
    }
  }

  protected transformError(error: any): Error {
    if (error.data?.message) {
      return new Error(error.data.message);
    }
    if (error.message) {
      return new Error(error.message);
    }
    return new Error('An unexpected error occurred');
  }

  async get<T>(url: string, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(url, { ...options, method: 'GET' });
  }

  async post<T>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(url, { ...options, method: 'POST', body: data });
  }

  async put<T>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(url, { ...options, method: 'PUT', body: data });
  }

  async patch<T>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(url, { ...options, method: 'PATCH', body: data });
  }

  async delete<T>(url: string, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(url, { ...options, method: 'DELETE' });
  }
}
