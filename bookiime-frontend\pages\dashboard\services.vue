<template>
  <div class="min-h-screen bg-neutral-50">
    <!-- Page Header -->
    <div class="bg-white border-b border-neutral-200 px-6 py-4">
      <div class="max-w-7xl mx-auto">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-neutral-900">Service Catalog</h1>
            <p class="text-neutral-600 mt-1">Manage your business services and pricing</p>
          </div>
          <Button @click="openCreateModal" class="flex items-center space-x-2">
            <Icon name="lucide:plus" class="w-4 h-4" />
            <span>Add Service</span>
          </Button>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-6 py-8">
      <!-- Services Table -->
      <DataTable
        :data="services"
        :columns="tableColumns"
        :loading="loading"
        :error="error"
        searchable
        search-placeholder="Search services..."
        @search="handleSearch"
        @sort="handleSort"
        @retry="fetchServices"
      >
        <!-- Custom cell renderers -->
        <template #cell-price="{ value }">
          <span class="font-medium text-green-600">${{ value }}</span>
        </template>

        <template #cell-duration="{ value }">
          <span class="text-neutral-600">{{ value }} min</span>
        </template>

        <!-- Actions column -->
        <template #actions="{ item }">
          <div class="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              @click="openEditModal(item)"
              class="text-blue-600 hover:text-blue-700"
            >
              <Icon name="lucide:edit" class="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              @click="confirmDelete(item.id)"
              class="text-red-600 hover:text-red-700"
            >
              <Icon name="lucide:trash-2" class="w-4 h-4" />
            </Button>
          </div>
        </template>
      </DataTable>
    </div>

    <!-- Service Form Modal -->
    <BaseModal
      v-model="showModal"
      :title="isEditing ? 'Edit Service' : 'Add New Service'"
      :loading="formLoading"
      size="md"
    >
      <form @submit.prevent="submitForm" class="space-y-6">
        <FormField
          v-model="form.name"
          label="Service Name"
          placeholder="Enter service name"
          required
          :error-message="formErrors.name"
        />

        <FormField
          v-model="form.description"
          label="Description"
          type="textarea"
          placeholder="Enter service description"
          required
          :rows="3"
          :error-message="formErrors.description"
        />

        <div class="grid grid-cols-2 gap-4">
          <FormField
            v-model="form.price"
            label="Price"
            type="number"
            placeholder="0.00"
            required
            :min="0"
            :step="0.01"
            :error-message="formErrors.price"
          >
            <template #prefix>
              <span class="text-neutral-500">$</span>
            </template>
          </FormField>

          <FormField
            v-model="form.duration"
            label="Duration"
            type="number"
            placeholder="30"
            required
            :min="1"
            :error-message="formErrors.duration"
          >
            <template #suffix>
              <span class="text-neutral-500">min</span>
            </template>
          </FormField>
        </div>
      </form>

      <template #footer>
        <Button variant="outline" @click="closeModal" :disabled="formLoading">
          Cancel
        </Button>
        <Button @click="submitForm" :disabled="formLoading">
          <div v-if="formLoading" class="flex items-center space-x-2">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            <span>Saving...</span>
          </div>
          <span v-else>{{ isEditing ? 'Update Service' : 'Create Service' }}</span>
        </Button>
      </template>
    </BaseModal>

    <!-- Delete Confirmation Modal -->
    <BaseModal
      v-model="showDeleteModal"
      title="Confirm Delete"
      size="sm"
      :loading="formLoading"
    >
      <div class="text-center">
        <Icon name="lucide:alert-triangle" class="w-12 h-12 text-red-500 mx-auto mb-4" />
        <p class="text-neutral-600 mb-6">
          Are you sure you want to delete this service? This action cannot be undone.
        </p>
      </div>

      <template #footer>
        <Button variant="outline" @click="showDeleteModal = false" :disabled="formLoading">
          Cancel
        </Button>
        <Button variant="destructive" @click="deleteService" :disabled="formLoading">
          <div v-if="formLoading" class="flex items-center space-x-2">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            <span>Deleting...</span>
          </div>
          <span v-else>Delete Service</span>
        </Button>
      </template>
    </BaseModal>
  </div>
</template>

<script setup lang="ts">
// Import types and services following SOLID principles
import type { Service, CreateServiceRequest } from '@/types/service.types';
import { ServiceApiService } from '@/services/service-api.service';

// Define page metadata
definePageMeta({
  layout: 'dashboard'
});

// SEO
useHead({
  title: 'Service Catalog - Bookiime',
  meta: [
    { name: 'description', content: 'Manage your business services and pricing' }
  ]
});

// Dependency Injection - Service instance
const serviceApi = new ServiceApiService();

// Reactive state management (Single Responsibility)
const services = ref<Service[]>([]);
const loading = ref(false);
const error = ref<string | null>(null);
const formLoading = ref(false);

// Modal state
const showModal = ref(false);
const showDeleteModal = ref(false);
const isEditing = ref(false);
const selectedServiceId = ref<string | null>(null);

// Form state with validation
const form = reactive<CreateServiceRequest>({
  name: '',
  description: '',
  price: 0,
  duration: 30,
});

const formErrors = reactive({
  name: '',
  description: '',
  price: '',
  duration: '',
});

// Table configuration (Open/Closed Principle - easily extensible)
const tableColumns = [
  {
    key: 'name',
    label: 'Service Name',
    sortable: true,
  },
  {
    key: 'description',
    label: 'Description',
    sortable: false,
  },
  {
    key: 'price',
    label: 'Price',
    sortable: true,
    formatter: (value: number) => `$${value.toFixed(2)}`,
  },
  {
    key: 'duration',
    label: 'Duration',
    sortable: true,
    formatter: (value: number) => `${value} min`,
  },
];

// Business logic methods (Interface Segregation Principle)
const validateForm = (): boolean => {
  // Reset errors
  Object.keys(formErrors).forEach(key => {
    formErrors[key as keyof typeof formErrors] = '';
  });

  let isValid = true;

  // Name validation
  if (!form.name.trim()) {
    formErrors.name = 'Service name is required';
    isValid = false;
  } else if (form.name.length < 2) {
    formErrors.name = 'Service name must be at least 2 characters';
    isValid = false;
  }

  // Description validation
  if (!form.description.trim()) {
    formErrors.description = 'Description is required';
    isValid = false;
  } else if (form.description.length < 10) {
    formErrors.description = 'Description must be at least 10 characters';
    isValid = false;
  }

  // Price validation
  if (form.price <= 0) {
    formErrors.price = 'Price must be greater than 0';
    isValid = false;
  }

  // Duration validation
  if (form.duration <= 0) {
    formErrors.duration = 'Duration must be greater than 0';
    isValid = false;
  }

  return isValid;
};

// API methods (Single Responsibility Principle)
const fetchServices = async (search?: string) => {
  try {
    loading.value = true;
    error.value = null;
    const response = await serviceApi.getServices({ search });
    if (response.success) {
      services.value = response.data;
    } else {
      throw new Error(response.message || 'Failed to fetch services');
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'An error occurred';
    console.error('Error fetching services:', err);
    $toast(error.value, { type: 'error' });
  } finally {
    loading.value = false;
  }
};

const createService = async (serviceData: CreateServiceRequest): Promise<boolean> => {
  try {
    formLoading.value = true;
    const response = await serviceApi.createService(serviceData);
    if (response.success) {
      services.value.push(response.data);
      $toast('Service created successfully', { type: 'success' });
      return true;
    } else {
      throw new Error(response.message || 'Failed to create service');
    }
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'An error occurred';
    $toast(errorMessage, { type: 'error' });
    console.error('Error creating service:', err);
    return false;
  } finally {
    formLoading.value = false;
  }
};

const updateService = async (id: string, serviceData: CreateServiceRequest): Promise<boolean> => {
  try {
    formLoading.value = true;
    const response = await serviceApi.updateService(id, serviceData);
    if (response.success) {
      const index = services.value.findIndex(s => s.id === id);
      if (index !== -1) {
        services.value[index] = response.data;
      }
      $toast('Service updated successfully', { type: 'success' });
      return true;
    } else {
      throw new Error(response.message || 'Failed to update service');
    }
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'An error occurred';
    $toast(errorMessage, { type: 'error' });
    console.error('Error updating service:', err);
    return false;
  } finally {
    formLoading.value = false;
  }
};

const deleteServiceById = async (id: string): Promise<boolean> => {
  try {
    formLoading.value = true;
    const response = await serviceApi.deleteService(id);
    if (response.success) {
      services.value = services.value.filter(s => s.id !== id);
      $toast('Service deleted successfully', { type: 'success' });
      return true;
    } else {
      throw new Error(response.message || 'Failed to delete service');
    }
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'An error occurred';
    $toast(errorMessage, { type: 'error' });
    console.error('Error deleting service:', err);
    return false;
  } finally {
    formLoading.value = false;
  }
};

// UI Methods (Interface Segregation Principle)
const resetForm = () => {
  form.name = '';
  form.description = '';
  form.price = 0;
  form.duration = 30;

  // Reset form errors
  Object.keys(formErrors).forEach(key => {
    formErrors[key as keyof typeof formErrors] = '';
  });
};

const openCreateModal = () => {
  resetForm();
  isEditing.value = false;
  showModal.value = true;
};

const openEditModal = (service: Service) => {
  resetForm();
  form.name = service.name;
  form.description = service.description;
  form.price = service.price;
  form.duration = service.duration;
  selectedServiceId.value = service.id;
  isEditing.value = true;
  showModal.value = true;
};

const closeModal = () => {
  showModal.value = false;
  resetForm();
  selectedServiceId.value = null;
  formLoading.value = false;
};

const submitForm = async () => {
  if (!validateForm()) {
    return;
  }

  let success = false;

  if (isEditing.value && selectedServiceId.value) {
    success = await updateService(selectedServiceId.value, { ...form });
  } else {
    success = await createService({ ...form });
  }

  if (success) {
    closeModal();
  }
};

const confirmDelete = (id: string) => {
  selectedServiceId.value = id;
  showDeleteModal.value = true;
};

const deleteService = async () => {
  if (selectedServiceId.value) {
    const success = await deleteServiceById(selectedServiceId.value);
    if (success) {
      showDeleteModal.value = false;
      selectedServiceId.value = null;
    }
  }
};

// Event handlers for DataTable
const handleSearch = (query: string) => {
  fetchServices(query);
};

const handleSort = ({ column, order }: { column: string; order: 'asc' | 'desc' }) => {
  // Sort is handled by DataTable component internally
  // In a real app, you might want to call API with sort parameters
  console.log('Sort:', column, order);
};

// Lifecycle hooks
onMounted(async () => {
  await fetchServices();

  // Sample data for development (remove in production)
  if (services.value.length === 0) {
    services.value = [
      {
        id: '1',
        name: 'Haircut',
        description: 'A standard haircut for any hair length.',
        price: 30,
        duration: 30
      },
      {
        id: '2',
        name: 'Manicure',
        description: 'A basic manicure service.',
        price: 25,
        duration: 45
      },
      {
        id: '3',
        name: 'Massage',
        description: 'A relaxing full-body massage.',
        price: 60,
        duration: 60
      },
      {
        id: '4',
        name: 'Facial',
        description: 'A rejuvenating facial treatment.',
        price: 50,
        duration: 60
      },
      {
        id: '5',
        name: 'Personal Training',
        description: 'One-on-one fitness training session.',
        price: 75,
        duration: 60
      }
    ];
  }
});
</script>