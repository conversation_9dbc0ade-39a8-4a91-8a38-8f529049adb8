<-- <template>
  <div class="p-6">
    <!-- <PERSON> Header -->
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-2xl font-bold text-neutral-900">Services</h1>
        <p class="text-neutral-600 mt-1">Manage all your services and pricing</p>
      </div>
      <NuxtLink
        to="/dashboard/services/new"
        class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
      >
        <Icon name="lucide:plus" class="w-4 h-4 mr-2" />
        New Service
      </NuxtLink>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg border border-neutral-200 p-6 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-neutral-700 mb-2">Status</label>
          <select class="w-full border border-neutral-300 rounded-lg px-3 py-2">
            <option value="">All Statuses</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
        </div>
        <div class="flex items-end">
          <button class="w-full px-4 py-2 bg-neutral-100 text-neutral-700 rounded-lg hover:bg-neutral-200 transition-colors">
            Apply Filters
          </button>
        </div>
      </div>
    </div>

    <!-- Services Table -->
    <div class="bg-white rounded-lg border border-neutral-200 overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-neutral-200">
          <thead class="bg-neutral-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                Name
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                Price
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                Status
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-neutral-200">
            <tr v-for="service in services" :key="service.id">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-neutral-900">{{ service.name }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-neutral-900">${{ service.price }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                  :class="{
                    'bg-green-100 text-green-800': service.status === 'active',
                    'bg-red-100 text-red-800': service.status === 'inactive'
                  }"
                >
                  {{ service.status }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                  <button class="text-primary-600 hover:text-primary-900">Edit</button>
                  <button class="text-red-600 hover:text-red-900">Delete</button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'dashboard'
})

// Meta tags
useHead({
  title: 'Services - Bookiime',
  meta: [
    { name: 'description', content: 'Manage your services and pricing' }
  ]
})

// Mock data - replace with actual API calls
const services = ref([
  { id: 1, name: 'Hair Cut', price: 50, status: 'active' },
  { id: 2, name: 'Massage Therapy', price: 80, status: 'active' },
  { id: 3, name: 'Consultation', price: 30, status: 'inactive' }
])
</script>

-->


<template>
  <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden" style='font-family: Inter, "Noto Sans", sans-serif;'>
    <div class="layout-container flex h-full grow flex-col">
      <!-- Header -->
      <header class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f5] px-10 py-3">
        <div class="flex items-center gap-8">
          <div class="flex items-center gap-4 text-[#111418]">
            <div class="size-4">
              <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M24 4H42V17.3333V30.6667H24V44H6V30.6667V17.3333H24V4Z" fill="currentColor"></path>
              </svg>
            </div>
            <h2 class="text-[#111418] text-lg font-bold leading-tight tracking-[-0.015em]">Bookiime</h2>
          </div>
          <div class="flex items-center gap-9">
            <a class="text-[#111418] text-sm font-medium leading-normal" href="#">Dashboard</a>
            <a class="text-[#111418] text-sm font-medium leading-normal" href="#">Bookings</a>
            <a class="text-[#111418] text-sm font-medium leading-normal" href="#">Customers</a>
            <a class="text-[#111418] text-sm font-medium leading-normal" href="#">Services</a>
            <a class="text-[#111418] text-sm font-medium leading-normal" href="#">Staff</a>
            <a class="text-[#111418] text-sm font-medium leading-normal" href="#">Reports</a>
          </div>
        </div>
        <div class="flex flex-1 justify-end gap-8">
          <label class="flex flex-col min-w-40 !h-10 max-w-64">
            <div class="flex w-full flex-1 items-stretch rounded-lg h-full">
              <div class="text-[#60758a] flex border-none bg-[#f0f2f5] items-center justify-center pl-4 rounded-l-lg border-r-0">
                <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                  <path d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"></path>
                </svg>
              </div>
              <input
                placeholder="Search"
                class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-lg text-[#111418] focus:outline-0 focus:ring-0 border-none bg-[#f0f2f5] focus:border-none h-full placeholder:text-[#60758a] px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
              />
            </div>
          </label>
          <div
            class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
            style='background-image: url("https://cdn.jsdelivr.net/gh/alohe/avatars/png/vibrent_1.png");'
          ></div>
        </div>
      </header>

      <!-- Main Content -->
      <div class="px-40 flex flex-1 justify-center py-5">
        <div class="layout-content-container flex flex-col max-w-[960px] flex-1">
          <!-- Page Header -->
          <div class="flex flex-wrap justify-between gap-3 p-4">
            <p class="text-[#111418] tracking-light text-[32px] font-bold leading-tight min-w-72">Service Catalog</p>
            <button
              @click="openCreateModal"
              class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-8 px-4 bg-[#f0f2f5] text-[#111418] text-sm font-medium leading-normal hover:bg-[#e1e5e9] transition-colors"
            >
              <span class="truncate">Add Service</span>
            </button>
          </div>

          <!-- Search Input -->
          <div class="px-4 py-3">
            <label class="flex flex-col min-w-40 h-12 w-full">
              <div class="flex w-full flex-1 items-stretch rounded-lg h-full">
                <div class="text-[#60758a] flex border-none bg-[#f0f2f5] items-center justify-center pl-4 rounded-l-lg border-r-0">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"></path>
                  </svg>
                </div>
                <input
                  v-model="searchQuery"
                  @input="debouncedSearch"
                  placeholder="Search services"
                  class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-lg text-[#111418] focus:outline-0 focus:ring-0 border-none bg-[#f0f2f5] focus:border-none h-full placeholder:text-[#60758a] px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
                />
              </div>
            </label>
          </div>

          <!-- Services Table -->
          <div class="px-4 py-3 @container">
            <div class="flex overflow-hidden rounded-lg border border-[#dbe0e6] bg-white">
              <table class="flex-1">
                <thead>
                  <tr class="bg-white">
                    <th class="px-4 py-3 text-left text-[#111418] w-[400px] text-sm font-medium leading-normal">
                      Service Name
                    </th>
                    <th class="px-4 py-3 text-left text-[#111418] w-[400px] text-sm font-medium leading-normal">
                      Description
                    </th>
                    <th class="px-4 py-3 text-left text-[#111418] w-[400px] text-sm font-medium leading-normal">Price</th>
                    <th class="px-4 py-3 text-left text-[#111418] w-[400px] text-sm font-medium leading-normal">
                      Duration
                    </th>
                    <th class="px-4 py-3 text-left text-[#111418] w-60 text-[#60758a] text-sm font-medium leading-normal">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <!-- Loading State -->
                  <tr v-if="loading" class="border-t border-t-[#dbe0e6]">
                    <td colspan="5" class="h-[72px] px-4 py-2 text-center text-[#60758a] text-sm">
                      Loading services...
                    </td>
                  </tr>

                  <!-- Error State -->
                  <tr v-else-if="error" class="border-t border-t-[#dbe0e6]">
                    <td colspan="5" class="h-[72px] px-4 py-2 text-center text-red-600 text-sm">
                      {{ error }}
                    </td>
                  </tr>

                  <!-- Empty State -->
                  <tr v-else-if="filteredServices.length === 0" class="border-t border-t-[#dbe0e6]">
                    <td colspan="5" class="h-[72px] px-4 py-2 text-center text-[#60758a] text-sm">
                      No services found.
                    </td>
                  </tr>

                  <!-- Service Rows -->
                  <tr
                    v-else
                    v-for="service in filteredServices"
                    :key="service.id"
                    class="border-t border-t-[#dbe0e6] hover:bg-gray-50 transition-colors"
                  >
                    <td class="h-[72px] px-4 py-2 w-[400px] text-[#111418] text-sm font-normal leading-normal">
                      {{ service.name }}
                    </td>
                    <td class="h-[72px] px-4 py-2 w-[400px] text-[#60758a] text-sm font-normal leading-normal">
                      {{ service.description }}
                    </td>
                    <td class="h-[72px] px-4 py-2 w-[400px] text-[#60758a] text-sm font-normal leading-normal">
                      ${{ service.price }}
                    </td>
                    <td class="h-[72px] px-4 py-2 w-[400px] text-[#60758a] text-sm font-normal leading-normal">
                      {{ service.duration }} minutes
                    </td>
                    <td class="h-[72px] px-4 py-2 w-60">
                      <button
                        @click="openEditModal(service)"
                        class="text-blue-600 hover:text-blue-800 text-sm font-bold leading-normal tracking-[0.015em] mr-2 transition-colors"
                      >
                        Edit
                      </button>
                      <span class="text-[#60758a]">|</span>
                      <button
                        @click="confirmDelete(service.id)"
                        class="text-red-600 hover:text-red-800 text-sm font-bold leading-normal tracking-[0.015em] ml-2 transition-colors"
                      >
                        Remove
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Service Form Modal -->
    <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <h3 class="text-lg font-bold mb-4 text-[#111418]">
          {{ isEditing ? 'Edit Service' : 'Add New Service' }}
        </h3>
        
        <form @submit.prevent="submitForm">
          <div class="mb-4">
            <label class="block text-sm font-medium text-[#111418] mb-2">Service Name</label>
            <input
              v-model="form.name"
              type="text"
              required
              class="w-full px-3 py-2 border border-[#dbe0e6] rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter service name"
            />
          </div>
          
          <div class="mb-4">
            <label class="block text-sm font-medium text-[#111418] mb-2">Description</label>
            <textarea
              v-model="form.description"
              required
              rows="3"
              class="w-full px-3 py-2 border border-[#dbe0e6] rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter service description"
            ></textarea>
          </div>
          
          <div class="mb-4">
            <label class="block text-sm font-medium text-[#111418] mb-2">Price ($)</label>
            <input
              v-model.number="form.price"
              type="number"
              min="0"
              step="0.01"
              required
              class="w-full px-3 py-2 border border-[#dbe0e6] rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="0.00"
            />
          </div>
          
          <div class="mb-6">
            <label class="block text-sm font-medium text-[#111418] mb-2">Duration (minutes)</label>
            <input
              v-model.number="form.duration"
              type="number"
              min="1"
              required
              class="w-full px-3 py-2 border border-[#dbe0e6] rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="30"
            />
          </div>
          
          <div class="flex justify-end gap-2">
            <button
              type="button"
              @click="closeModal"
              class="px-4 py-2 text-[#60758a] border border-[#dbe0e6] rounded-md hover:bg-[#f0f2f5] transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              :disabled="formLoading"
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              {{ formLoading ? 'Saving...' : (isEditing ? 'Update' : 'Create') }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-sm mx-4">
        <h3 class="text-lg font-bold mb-4 text-[#111418]">Confirm Delete</h3>
        <p class="text-[#60758a] mb-6">Are you sure you want to delete this service? This action cannot be undone.</p>
        
        <div class="flex justify-end gap-2">
          <button
            @click="showDeleteModal = false"
            class="px-4 py-2 text-[#60758a] border border-[#dbe0e6] rounded-md hover:bg-[#f0f2f5] transition-colors"
          >
            Cancel
          </button>
          <button
            @click="deleteService"
            :disabled="formLoading"
            class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 transition-colors"
          >
            {{ formLoading ? 'Deleting...' : 'Delete' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Interfaces defined inline
interface Service {
  id: string
  name: string
  description: string
  price: number
  duration: number
  tenantId?: string
  createdAt?: Date
  updatedAt?: Date
}

interface CreateServiceRequest {
  name: string
  description: string
  price: number
  duration: number
}

interface ApiResponse<T> {
  data: T
  message?: string
  success: boolean
}

// SEO
useHead({
  title: 'Service Catalog - Bookiime',
  meta: [
    { name: 'description', content: 'Manage your business services and pricing' }
  ]
})

// API Service Class (Single Responsibility Principle)
class ServiceApiService {
  private baseUrl: string

  constructor(baseUrl: string = '/api') {
    this.baseUrl = baseUrl
  }

  async createService(service: CreateServiceRequest): Promise<ApiResponse<Service>> {
    const response = await $fetch<ApiResponse<Service>>(`${this.baseUrl}/services`, {
      method: 'POST',
      body: service,
    })
    return response
  }

  async getServices(search?: string): Promise<ApiResponse<Service[]>> {
    const query = search ? `?search=${encodeURIComponent(search)}` : ''
    const response = await $fetch<ApiResponse<Service[]>>(`${this.baseUrl}/services${query}`)
    return response
  }

  async getServicesByTenant(tenantId: string): Promise<ApiResponse<Service[]>> {
    const response = await $fetch<ApiResponse<Service[]>>(
      `${this.baseUrl}/services/tenant?tenantId=${tenantId}`
    )
    return response
  }

  async getServiceById(id: string): Promise<ApiResponse<Service>> {
    const response = await $fetch<ApiResponse<Service>>(`${this.baseUrl}/services/${id}`)
    return response
  }

  async updateService(id: string, service: CreateServiceRequest): Promise<ApiResponse<Service>> {
    const response = await $fetch<ApiResponse<Service>>(`${this.baseUrl}/services/${id}`, {
      method: 'PUT',
      body: service,
    })
    return response
  }

  async deleteServiceById(id: string): Promise<ApiResponse<void>> {
    const response = await $fetch<ApiResponse<void>>(`${this.baseUrl}/services/${id}`, {
      method: 'DELETE',
    })
    return response
  }
}

// State Management (Open/Closed Principle - easily extensible)
const services = ref<Service[]>([])
const loading = ref(false)
const error = ref<string | null>(null)
const searchQuery = ref('')
const showModal = ref(false)
const showDeleteModal = ref(false)
const isEditing = ref(false)
const formLoading = ref(false)
const selectedServiceId = ref<string | null>(null)

// Form state
const form = reactive<CreateServiceRequest>({
  name: '',
  description: '',
  price: 0,
  duration: 30,
})

// Service instance (Dependency Injection)
const apiService = new ServiceApiService()

// Computed properties
const filteredServices = computed(() => {
  if (!searchQuery.value) return services.value
  const query = searchQuery.value.toLowerCase()
  return services.value.filter(service =>
    service.name.toLowerCase().includes(query) ||
    service.description.toLowerCase().includes(query)
  )
})

// Methods (Interface Segregation - focused methods)
const fetchServices = async (search?: string) => {
  try {
    loading.value = true
    error.value = null
    const response = await apiService.getServices(search)
    if (response.success) {
      services.value = response.data
    } else {
      throw new Error(response.message || 'Failed to fetch services')
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'An error occurred'
    console.error('Error fetching services:', err)
  } finally {
    loading.value = false
  }
}

const createService = async (serviceData: CreateServiceRequest): Promise<boolean> => {
  try {
    formLoading.value = true
    const response = await apiService.createService(serviceData)
    if (response.success) {
      services.value.push(response.data)
      return true
    } else {
      throw new Error(response.message || 'Failed to create service')
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'An error occurred'
    console.error('Error creating service:', err)
    return false
  } finally {
    formLoading.value = false
  }
}

const updateService = async (id: string, serviceData: CreateServiceRequest): Promise<boolean> => {
  try {
    formLoading.value = true
    const response = await apiService.updateService(id, serviceData)
    if (response.success) {
      const index = services.value.findIndex(s => s.id === id)
      if (index !== -1) {
        services.value[index] = response.data
      }
      return true
    } else {
      throw new Error(response.message || 'Failed to update service')
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'An error occurred'
    console.error('Error updating service:', err)
    return false
  } finally {
    formLoading.value = false
  }
}

const deleteServiceById = async (id: string): Promise<boolean> => {
  try {
    formLoading.value = true
    const response = await apiService.deleteServiceById(id)
    if (response.success) {
      services.value = services.value.filter(s => s.id !== id)
      return true
    } else {
      throw new Error(response.message || 'Failed to delete service')
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'An error occurred'
    console.error('Error deleting service:', err)
    return false
  } finally {
    formLoading.value = false
  }
}

// UI Methods
const resetForm = () => {
  form.name = ''
  form.description = ''
  form.price = 0
  form.duration = 30
}

const openCreateModal = () => {
  resetForm()
  isEditing.value = false
  showModal.value = true
}

const openEditModal = (service: Service) => {
  form.name = service.name
  form.description = service.description
  form.price = service.price
  form.duration = service.duration
  selectedServiceId.value = service.id
  isEditing.value = true
  showModal.value = true
}

const closeModal = () => {
  showModal.value = false
  resetForm()
  selectedServiceId.value = null
  formLoading.value = false
}

const submitForm = async () => {
  let success = false
  
  if (isEditing.value && selectedServiceId.value) {
    success = await updateService(selectedServiceId.value, { ...form })
  } else {
    success = await createService({ ...form })
  }
  
  if (success) {
    closeModal()
  }
}

const confirmDelete = (id: string) => {
  selectedServiceId.value = id
  showDeleteModal.value = true
}

const deleteService = async () => {
  if (selectedServiceId.value) {
    const success = await deleteServiceById(selectedServiceId.value)
    if (success) {
      showDeleteModal.value = false
      selectedServiceId.value = null
    }
  }
}

// Debounced search (Liskov Substitution - can replace with any debounce implementation)
const debouncedSearch = useDebounceFn(() => {
  // Search is handled by computed property for simplicity
  // In real app, you might want to call API here
}, 300)

// Lifecycle
onMounted(() => {
  fetchServices()
})

// Sample data for development (remove in production)
onMounted(() => {
  // Initialize with sample data if no API
  if (services.value.length === 0) {
    services.value = [
      {
        id: '1',
        name: 'Haircut',
        description: 'A standard haircut for any hair length.',
        price: 30,
        duration: 30
      },
      {
        id: '2',
        name: 'Manicure',
        description: 'A basic manicure service.',
        price: 25,
        duration: 45
      },
      {
        id: '3',
        name: 'Massage',
        description: 'A relaxing full-body massage.',
        price: 60,
        duration: 60
      },
      {
        id: '4',
        name: 'Facial',
        description: 'A rejuvenating facial treatment.',
        price: 50,
        duration: 60
      },
      {
        id: '5',
        name: 'Personal Training',
        description: 'One-on-one fitness training session.',
        price: 75,
        duration: 60
      }
    ]
  }
})
</script>

<style scoped>
/* Custom styles if needed */
.transition-colors {
  transition: color 0.2s ease, background-color 0.2s ease;
}
</style>