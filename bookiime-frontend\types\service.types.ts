// Service-related types following Single Responsibility Principle

export interface Service {
  id: string;
  name: string;
  description: string;
  price: number;
  duration: number;
  tenantId?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface CreateServiceRequest {
  name: string;
  description: string;
  price: number;
  duration: number;
}

export interface UpdateServiceRequest extends CreateServiceRequest {
  id: string;
}

export interface ServiceSearchParams {
  search?: string;
  tenantId?: string;
  page?: number;
  limit?: number;
}

// Service validation rules
export interface ServiceValidationRules {
  name: {
    required: boolean;
    minLength: number;
    maxLength: number;
  };
  description: {
    required: boolean;
    minLength: number;
    maxLength: number;
  };
  price: {
    required: boolean;
    min: number;
    max: number;
  };
  duration: {
    required: boolean;
    min: number;
    max: number;
  };
}

export const DEFAULT_SERVICE_VALIDATION: ServiceValidationRules = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 100,
  },
  description: {
    required: true,
    minLength: 10,
    maxLength: 500,
  },
  price: {
    required: true,
    min: 0,
    max: 10000,
  },
  duration: {
    required: true,
    min: 1,
    max: 480, // 8 hours max
  },
};
