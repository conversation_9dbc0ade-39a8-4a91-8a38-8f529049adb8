<template>
  <Card>
    <CardHeader>
      <CardTitle>Profile Information</CardTitle>
      <CardDescription>
        Update your personal information and account details
      </CardDescription>
    </CardHeader>
    
    <CardContent>
      <form @submit.prevent="handleSubmit" class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Name Field -->
          <FormField
            v-model="form.name"
            label="Full Name"
            placeholder="Enter your full name"
            required
            :error-message="errors.name"
            :disabled="loading"
          />

          <!-- Email Field -->
          <FormField
            v-model="form.email"
            label="Email Address"
            type="email"
            placeholder="Enter your email"
            required
            :error-message="errors.email"
            :disabled="loading"
          >
            <template #prefix>
              <Icon name="lucide:mail" class="w-4 h-4" />
            </template>
          </FormField>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Phone Field -->
          <FormField
            v-model="form.phone"
            label="Phone Number"
            type="tel"
            placeholder="Enter your phone number"
            :error-message="errors.phone"
            :disabled="loading"
          >
            <template #prefix>
              <Icon name="lucide:phone" class="w-4 h-4" />
            </template>
          </FormField>

          <!-- Subdomain Field -->
          <FormField
            v-model="form.subdomain"
            label="Subdomain"
            placeholder="your-business"
            :error-message="errors.subdomain"
            :disabled="loading"
            help-text="This will be your booking page URL: your-business.bookiime.com"
          >
            <template #prefix>
              <span class="text-neutral-500 text-sm">bookiime.com/</span>
            </template>
          </FormField>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-between pt-6 border-t border-neutral-200">
          <div class="text-sm text-neutral-500">
            <Icon name="lucide:info" class="w-4 h-4 inline mr-1" />
            Changes will be saved automatically
          </div>
          
          <div class="flex items-center space-x-3">
            <Button
              type="button"
              variant="outline"
              @click="resetForm"
              :disabled="loading || !isDirty"
            >
              Reset
            </Button>
            <Button
              type="submit"
              :loading="loading"
              :disabled="!isDirty || !isValid"
            >
              Save Changes
            </Button>
          </div>
        </div>
      </form>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import type { UpdateProfileRequest, UserProfile } from '@/types/user.types';

interface Props {
  profile: UserProfile | null;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
});

const emit = defineEmits<{
  'update:profile': [data: UpdateProfileRequest];
  'validate-email': [email: string];
  'validate-subdomain': [subdomain: string];
}>();

// Form state
const form = reactive<UpdateProfileRequest>({
  name: '',
  email: '',
  phone: '',
  subdomain: '',
});

const errors = reactive({
  name: '',
  email: '',
  phone: '',
  subdomain: '',
});

// Computed properties
const isDirty = computed(() => {
  if (!props.profile) return false;
  
  return (
    form.name !== props.profile.name ||
    form.email !== props.profile.email ||
    form.phone !== (props.profile.phone || '') ||
    form.subdomain !== (props.profile.subdomain || '')
  );
});

const isValid = computed(() => {
  return !Object.values(errors).some(error => error !== '') &&
         form.name.trim() !== '' &&
         form.email.trim() !== '';
});

// Methods
const validateForm = (): boolean => {
  // Reset errors
  Object.keys(errors).forEach(key => {
    errors[key as keyof typeof errors] = '';
  });

  let isValid = true;

  // Name validation
  if (!form.name.trim()) {
    errors.name = 'Name is required';
    isValid = false;
  } else if (form.name.length < 2) {
    errors.name = 'Name must be at least 2 characters';
    isValid = false;
  }

  // Email validation
  if (!form.email.trim()) {
    errors.email = 'Email is required';
    isValid = false;
  } else {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(form.email)) {
      errors.email = 'Please enter a valid email address';
      isValid = false;
    }
  }

  // Phone validation (optional)
  if (form.phone && form.phone.trim()) {
    const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
    if (!phoneRegex.test(form.phone)) {
      errors.phone = 'Please enter a valid phone number';
      isValid = false;
    }
  }

  // Subdomain validation (optional)
  if (form.subdomain && form.subdomain.trim()) {
    const subdomainRegex = /^[a-z0-9]([a-z0-9\-]{0,61}[a-z0-9])?$/;
    if (!subdomainRegex.test(form.subdomain)) {
      errors.subdomain = 'Subdomain must contain only lowercase letters, numbers, and hyphens';
      isValid = false;
    }
  }

  return isValid;
};

const resetForm = () => {
  if (props.profile) {
    form.name = props.profile.name;
    form.email = props.profile.email;
    form.phone = props.profile.phone || '';
    form.subdomain = props.profile.subdomain || '';
  }
  
  // Reset errors
  Object.keys(errors).forEach(key => {
    errors[key as keyof typeof errors] = '';
  });
};

const handleSubmit = () => {
  if (validateForm()) {
    emit('update:profile', { ...form });
  }
};

// Watch for profile changes
watch(() => props.profile, (newProfile) => {
  if (newProfile) {
    resetForm();
  }
}, { immediate: true });

// Debounced validation for email and subdomain
const debouncedEmailValidation = useDebounceFn((email: string) => {
  if (email && email !== props.profile?.email) {
    emit('validate-email', email);
  }
}, 500);

const debouncedSubdomainValidation = useDebounceFn((subdomain: string) => {
  if (subdomain && subdomain !== props.profile?.subdomain) {
    emit('validate-subdomain', subdomain);
  }
}, 500);

watch(() => form.email, debouncedEmailValidation);
watch(() => form.subdomain, debouncedSubdomainValidation);
</script>
